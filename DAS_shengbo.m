%% ========================================================================
%  三维弹性波有限差分时域（FDTD）正演模拟程序
%  3D Elastic Wave Finite-Difference Time-Domain Forward Modeling
%% ========================================================================
clc;clear;close all;

%% ========================================================================
%  第一部分：初始化设置
%% ========================================================================

% 网格参数
nx = 64;
ny = 64;
nz = 512;

% 物理区域尺寸
Lx = 1.0;
Ly = 1.0;
Lz = 6.0;

% 时间步数
nt = 2500;

% 网格间距
dx = Lx/(nx-1);
dy = Ly/(ny-1);
dz = Lz/(nz-1);

%% ========================================================================
%  第二部分：物理模型
%% ========================================================================

% 流体参数
vp_fluid = 1500;
vs_fluid = 0;
rho_fluid = 1000;

% 岩石参数
vp_rock = 4000;
vs_rock = 2300;
rho_rock = 2500;

% 流体弹性模量
G_fluid = rho_fluid * vs_fluid^2;
K_fluid = rho_fluid * vp_fluid^2 - 4*G_fluid/3;

% 岩石弹性模量
G_rock = rho_rock * vs_rock^2;
K_rock = rho_rock * vp_rock^2 - 4*G_rock/3;

% 井筒几何参数
well_radius = 0.1;
well_center_x = Lx / 2;
well_center_y = Ly / 2;

% 材料参数数组初始化
K = zeros(nx, ny, nz);
G = zeros(nx, ny, nz);
rho = zeros(nx, ny, nz);

% 材料参数空间分配（井筒内外区分）
for i = 1:nx
    for j = 1:ny
        x = (i-1) * dx;
        y = (j-1) * dy;
        
        distance_to_well = sqrt((x - well_center_x)^2 + (y - well_center_y)^2);
        
        if distance_to_well <= well_radius
            K(i, j, :) = K_fluid;
            G(i, j, :) = G_fluid;
            rho(i, j, :) = rho_fluid;
        else
            K(i, j, :) = K_rock;
            G(i, j, :) = G_rock;
            rho(i, j, :) = rho_rock;
        end
    end
end

% CFL稳定性条件和时间步长
cfl = 0.6;
vp_max = max(vp_rock, vp_fluid);
dt = min([dx, dy, dz]) / vp_max * cfl;

fprintf('=== 声波测井物理参数设置 ===\n');
fprintf('井筒内流体：vp=%.0f m/s, vs=%.0f m/s, ρ=%.0f kg/m³\n', vp_fluid, vs_fluid, rho_fluid);
fprintf('地层岩石：vp=%.0f m/s, vs=%.0f m/s, ρ=%.0f kg/m³\n', vp_rock, vs_rock, rho_rock);
fprintf('井筒半径：%.1f m, 时间步长：%.2e s\n', well_radius, dt);

%% ========================================================================
%  第三部分：震源设置
%% ========================================================================

% 震源位置
source_x = Lx / 2;
source_y = Ly / 2;
source_z = 1.0;

% 震源网格索引
isx = round(source_x / dx) + 1;
isy = round(source_y / dy) + 1;
isz = round(source_z / dz) + 1;

% Ricker子波参数
f0 = 8000;
t0 = 1.0/f0;

% 震源函数生成
src = zeros(nt,1);

for it = 1:nt
    t = it * dt - t0;
    src(it) = (1 - 2 * pi^2 * f0^2 * t^2) * exp(-pi^2 * f0^2 * t^2);
end

% 震源数据保存
fid = fopen('src.dat','wb'); fwrite(fid,src(:),'double'); fclose(fid);

%% ========================================================================
%  第四部分：检波器设置
%% ========================================================================

% 多检波器阵列参数 - 增加检波器数量
first_receiver_offset = 3.0;  % 第一个检波器在震源上方2米
receiver_spacing = 0.15;      % 检波器间距0.15米

% 计算检波器数量（从第一个检波器开始，每0.15米向上布置到网格顶部）
max_z_position = source_z + first_receiver_offset;
while max_z_position < Lz - 0.3  % 留出边界余量
    max_z_position = max_z_position + receiver_spacing;
end
num_receivers = floor((max_z_position - (source_z + first_receiver_offset)) / receiver_spacing) + 1;
fprintf('检波器数量：%d个\n', num_receivers);

% 检波器位置计算（相对于震源）
receiver_x = repmat(source_x, num_receivers, 1);
receiver_y = repmat(source_y, num_receivers, 1);
receiver_z = zeros(num_receivers, 1);
for i = 1:num_receivers
    receiver_z(i) = source_z + first_receiver_offset + (i-1) * receiver_spacing;
end

% 检波器网格索引
irx = round(receiver_x / dx) + 1;
iry = round(receiver_y / dy) + 1;
irz = round(receiver_z / dz) + 1;

% 数据存储初始化（8个检波器）
receiver_data = zeros(nt, num_receivers);
time_axis = (0:nt-1) * dt;

% 源距计算（用于显示）
source_distances = receiver_z - source_z;

%% ========================================================================
%  第四部分B：DAS系统设置
%% ========================================================================

% DAS系统参数设置
num_das_gauges = num_receivers;  % DAS标距点数量与检波器数量一致
gauge_length = 0.4;              % 标距长度(米)，可调整
gauge_overlap = 0.5;             % 标距重合比例(0-1之间)，本程序未使用
                                 % 实际重合率 = (0.4 - 0.15) / 0.4 = 62.5%

% DAS标距中心位置（与检波器位置完全对应）
das_gauge_centers = receiver_z;  % 每个标距中心位置与检波器位置一致

% DAS数据存储初始化
das_strain_rate = zeros(nt, num_das_gauges);  % 应变率数据
das_gauge_start_z = zeros(num_das_gauges, 1); % 每个标距起始位置
das_gauge_end_z = zeros(num_das_gauges, 1);   % 每个标距结束位置

% 计算每个DAS标距的起始和结束位置
for i = 1:num_das_gauges
    das_gauge_start_z(i) = das_gauge_centers(i) - gauge_length/2;
    das_gauge_end_z(i) = das_gauge_centers(i) + gauge_length/2;
end

% DAS标距网格索引计算
das_start_idx = round(das_gauge_start_z / dz) + 1;
das_end_idx = round(das_gauge_end_z / dz) + 1;

% 确保索引在有效范围内
das_start_idx = max(1, min(das_start_idx, nz));
das_end_idx = max(1, min(das_end_idx, nz));

fprintf('=== DAS系统参数设置 ===\n');
fprintf('DAS标距数量：%d个\n', num_das_gauges);
fprintf('标距长度：%.2f米\n', gauge_length);
fprintf('DAS覆盖范围：%.2f米 - %.2f米\n', das_gauge_centers(1), das_gauge_centers(end));

% 检波器位置验证
for i = 1:num_receivers
    if irx(i) < 1 || irx(i) > nx || iry(i) < 1 || iry(i) > ny || irz(i) < 1 || irz(i) > nz
        warning('检波器%d位置超出计算域范围！', i);
        fprintf('检波器%d位置：(%.2f, %.2f, %.2f)m\n', i, receiver_x(i), receiver_y(i), receiver_z(i));
        fprintf('网格索引：(%d, %d, %d)\n', irx(i), iry(i), irz(i));
        fprintf('网格范围：(1-%d, 1-%d, 1-%d)\n', nx, ny, nz);
    end
end

% 第二个检波点实时波形显示初始化
figure('Name', '第二个检波点实时波形', 'Position', [100 100 800 400]);
h_waveform = plot(time_axis*1e6, zeros(nt,1), 'b-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('压力 (Pa)');
title(sprintf('第二个检波点实时波形 (源距: %.2fm)', source_distances(2)));
grid on;
xlim([0 max(time_axis)*1e6]);
ylim([-1e-3, 1e-3]);  % 初始压力范围，会自动调整

%% ========================================================================
%  第五部分：边界条件
%% ========================================================================

% PML权重初始化
weights_x = ones(nx+1,1);
weights_y = ones(ny+1,1);
weights_z = ones(nz+1,1);

% PML层参数
lpml = 15;

% PML衰减系数数组
pml_x = zeros(nx, 1);
pml_y = zeros(ny, 1);
pml_z = zeros(nz, 1);
pml_width = lpml;

% PML衰减系数计算
for i = 1:lpml
    R = 1e-9;
    
    vmax = max(sqrt((K + 4/3*G)./rho), [], 'all');
    
    d_normalized = (lpml - i + 1) / lpml;
    
    sigma(i) = (3/2) * (vmax/dx) * log(1/R) * d_normalized^2;
    
    pml_x(i) = sigma(i);
    pml_x(nx-i+1) = sigma(i);

    pml_y(i) = sigma(i);
    pml_y(ny-i+1) = sigma(i);

    pml_z(i) = sigma(i);
    pml_z(nz-i+1) = sigma(i);
end

% PML权重计算
for i = 1:lpml
    damping_factor_x = exp(-pml_x(i) * dt);
    weights_x(i) = damping_factor_x;
    
    damping_factor_y = exp(-pml_y(i) * dt);
    weights_y(i) = damping_factor_y;
    
    damping_factor_z = exp(-pml_z(i) * dt);
    weights_z(i) = damping_factor_z;
    
    weights_x(nx-i+1) = damping_factor_x;
    weights_y(ny-i+1) = damping_factor_y;
    weights_z(nz-i+1) = damping_factor_z;
end

% PML权重插值和重塑
weights_xP = 0.5*(weights_x(1:end-1)+weights_x(2:end));
weights_yP = 0.5*(weights_y(1:end-1)+weights_y(2:end));
weights_zP = 0.5*(weights_z(1:end-1)+weights_z(2:end));

weights_yP = reshape(weights_yP,1,ny);
weights_zP = reshape(weights_zP,1,1,nz);
weights_y = reshape(weights_y,1,ny+1);
weights_z = reshape(weights_z,1,1,nz+1);

% 剪切模量插值（交错网格）
G_tau_xy = zeros(nx+1, ny+1, nz);
G_tau_xy(2:end-1,2:end-1,:) = (1/4*(1./G(1:end-1,1:end-1,:) + 1./G(2:end,2:end,:) + 1./G(2:end,1:end-1,:) + 1./G(1:end-1,2:end,:)) ).^(-1);
G_tau_xz = zeros(nx+1, ny, nz+1);
G_tau_xz(2:end-1,:,2:end-1) = (1/4*(1./G(1:end-1,:,1:end-1) + 1./G(2:end,:,2:end) + 1./G(2:end,:,1:end-1) + 1./G(1:end-1,:,2:end)) ).^(-1);
G_tau_yz = zeros(nx, ny+1, nz+1);
G_tau_yz(:,2:end-1,2:end-1) = (1/4*(1./G(:,1:end-1,1:end-1) + 1./G(:,2:end,2:end) + 1./G(:,2:end,1:end-1) + 1./G(:,1:end-1,2:end)) ).^(-1);

% 密度插值（交错网格）
rho_x = zeros(nx+1, ny, nz);
rho_y = zeros(nx, ny+1, nz);
rho_z = zeros(nx, ny, nz+1);

rho_x(2:end-1,:,:) = 0.5 .* (rho(1:end-1,:,:) + rho(2:end,:,:));
rho_x(1,:,:) = rho(1,:,:);
rho_x(end,:,:) = rho(end,:,:);

rho_y(:,2:end-1,:) = 0.5 .* (rho(:,1:end-1,:) + rho(:,2:end,:));
rho_y(:,1,:) = rho(:,1,:);
rho_y(:,end,:) = rho(:,end,:);

rho_z(:,:,2:end-1) = 0.5 .* (rho(:,:,1:end-1) + rho(:,:,2:end));
rho_z(:,:,1) = rho(:,:,1);
rho_z(:,:,end) = rho(:,:,end);

fileList = {'K.dat', 'G.dat', 'rho.dat'};
varList = {K, G, rho};
for i = 1:3
    fid = fopen(fileList{i},'wb'); fwrite(fid, varList{i}(:), 'double'); fclose(fid);
end

parameter_1 = [dx dy dz dt isx isy isz];
fid = fopen('parameter_1.dat','wb'); fwrite(fid, parameter_1, 'double'); fclose(fid);

%% ========================================================================
%  第六部分：场变量初始化
%% ========================================================================

% 速度场初始化（交错网格）
Vx = zeros(nx+1, ny, nz);
Vy = zeros(nx, ny+1, nz);
Vz = zeros(nx, ny, nz+1);

% 正应力场初始化
tau_xx = zeros(nx, ny, nz);
tau_yy = zeros(nx, ny, nz);
tau_zz = zeros(nx, ny, nz);

% 剪切应力场初始化（交错网格）
tau_xy = zeros(nx+1, ny+1, nz);
tau_xz = zeros(nx+1, ny, nz+1);
tau_yz = zeros(nx, ny+1, nz+1);

% 压力场初始化
Pr = zeros(nx, ny, nz);

%% ========================================================================
%  第六部分B：实时显示初始化
%% ========================================================================

% 创建实时显示窗口
figure('Position', [100, 100, 1200, 600]);

% 子图1：检波器波形（黑色）
subplot(2,1,1);
h_geophone = plot(time_axis*1e6, zeros(nt,1), 'k-', 'LineWidth', 1.5);
xlabel('时间 (μs)', 'FontSize', 12);
ylabel('压力幅度', 'FontSize', 12);
title('传统检波器波形 (第2个检波器)', 'FontSize', 14);
grid on;
xlim([0, time_axis(end)*1e6]);

% 子图2：DAS波形（红色）
subplot(2,1,2);
h_das = plot(time_axis*1e6, zeros(nt,1), 'r-', 'LineWidth', 1.5);
xlabel('时间 (μs)', 'FontSize', 12);
ylabel('应变率', 'FontSize', 12);
title('DAS应变率波形 (第2个标距)', 'FontSize', 14);
grid on;
xlim([0, time_axis(end)*1e6]);

drawnow;


%% ========================================================================
%  第七部分：时间循环
%% ========================================================================

for it = 1:nt
    it;
    
    % 震源加载
    Vz(isx, isy, isz) = Vz(isx, isy, isz) + src(it);
    
    % 检波器数据采集（8个检波器）
    for i = 1:num_receivers
        if irx(i) >= 1 && irx(i) <= nx && iry(i) >= 1 && iry(i) <= ny && irz(i) >= 1 && irz(i) <= nz
            receiver_data(it, i) = Pr(irx(i), iry(i), irz(i));
        end
    end
    
    % DAS应变率数据采集
    for i = 1:num_das_gauges
        % 获取标距起始和结束位置的网格索引
        start_idx = das_start_idx(i);
        end_idx = das_end_idx(i);
        
        % 确保索引在有效范围内
        if start_idx >= 1 && start_idx <= nz && end_idx >= 1 && end_idx <= nz && start_idx < end_idx
            % 计算轴向应变率：使用Vz速度场计算应变率
            % 应变率 = (V_end - V_start) / gauge_length
            vz_start = Vz(isx, isy, start_idx);
            vz_end = Vz(isx, isy, end_idx);
            das_strain_rate(it, i) = (vz_end - vz_start) / gauge_length;
        else
            das_strain_rate(it, i) = 0;  % 如果索引无效，设为0
        end
    end
    
    % 实时显示更新（同步显示检波器和DAS波形）
    if mod(it, 10) == 0 || it == nt
        % 获取第二个检波器和第二个DAS标距的数据
        if num_receivers >= 2 && num_das_gauges >= 2
            geophone_data = receiver_data(1:it, 2);  % 第二个检波器
            das_data = das_strain_rate(1:it, 2);     % 第二个DAS标距
            
            % 更新检波器波形显示（黑色）
            set(h_geophone, 'XData', time_axis(1:it)*1e6, 'YData', geophone_data);
            
            % 更新DAS波形显示（红色）
            set(h_das, 'XData', time_axis(1:it)*1e6, 'YData', das_data);
            
            % 自动调整y轴范围
            subplot(2,1,1);
            if max(abs(geophone_data)) > 0
                ylim([-1.2*max(abs(geophone_data)), 1.2*max(abs(geophone_data))]);
            end
            
            subplot(2,1,2);
            if max(abs(das_data)) > 0
                ylim([-1.2*max(abs(das_data)), 1.2*max(abs(das_data))]);
            end
            
            drawnow;
        end
        
        if mod(it, 50) == 0
            fprintf('时间步: %d/%d (%.1f%%), 检波器值: %.2e, DAS值: %.2e\n', ...
                    it, nt, 100*it/nt, receiver_data(it, min(2, num_receivers)), ...
                    das_strain_rate(it, min(2, num_das_gauges)));
        end
    end
    
    % 进度显示
    if mod(it, 10) == 0
        fprintf('.');
        if mod(it, 100) == 0
            fprintf(' [%d/%d]\n', it, nt);
        end
    end
    
    % 应力场更新（FDTD算法）
    divV = diff(Vx,1,1)/dx + diff(Vy,1,2)/dy + diff(Vz,1,3)/dz;
    
    Pr = (Pr - K.*divV*dt);
    
    tau_xx = (tau_xx + 2*G.*dt.*( diff(Vx,1,1)/dx - 1/3*divV ));
    tau_yy = (tau_yy + 2*G.*dt.*( diff(Vy,1,2)/dy - 1/3*divV ));
    tau_zz = (tau_zz + 2*G.*dt.*( diff(Vz,1,3)/dz - 1/3*divV ));
    
    tau_xy(2:end-1,2:end-1,:) = (tau_xy(2:end-1,2:end-1,:) + G_tau_xy(2:end-1,2:end-1,:).*dt.*( diff(Vy(:, 2:end-1, :),1,1)/dx + diff(Vx(2:end-1, :, :),1,2)/dy ));
    tau_xz(2:end-1,:,2:end-1) = (tau_xz(2:end-1,:,2:end-1) + G_tau_xz(2:end-1,:,2:end-1).*dt.*( diff(Vz(:, :, 2:end-1),1,1)/dx + diff(Vx(2:end-1, :, :),1,3)/dz ));
    tau_yz(:,2:end-1,2:end-1) = (tau_yz(:,2:end-1,2:end-1) + G_tau_yz(:,2:end-1,2:end-1).*dt.*( diff(Vy(:, 2:end-1, :),1,3)/dz + diff(Vz(:, :, 2:end-1),1,2)/dy ));
    
    % PML边界条件应用（z方向）
    for i = 1:nx
        for j=1:ny
            Pr(i,j,:) = Pr(i,j,:) .*weights_zP;
            tau_xx(i,j,:) = tau_xx(i,j,:) .*weights_zP;
            tau_yy(i,j,:) = tau_yy(i,j,:) .*weights_zP;
            tau_zz(i,j,:) = tau_zz(i,j,:) .*weights_zP;
            tau_xy(i,j,:) = tau_xy(i,j,:) .*weights_zP;
            tau_xz(i,j,2:end-1) = tau_xz(i,j,2:end-1) .*weights_zP(2:end);
            tau_yz(i,j,2:end-1) = tau_yz(i,j,2:end-1) .*weights_zP(2:end);
        end
    end
    
    % PML边界条件应用（y方向）
    for i = 1:nx
        for k = 1:nz
            Pr(i,:,k) = Pr(i,:,k) .*weights_yP;
            tau_xx(i,:,k) = tau_xx(i,:,k) .*weights_yP;
            tau_yy(i,:,k) = tau_yy(i,:,k) .*weights_yP;
            tau_zz(i,:,k) = tau_zz(i,:,k) .*weights_yP;
            tau_xy(i,2:end-1,k) = tau_xy(i,2:end-1,k) .*weights_yP(2:end);
            tau_xz(i,:,k) = tau_xz(i,:,k) .*weights_yP;
            tau_yz(i,2:end-1,k) = tau_yz(i,2:end-1,k) .*weights_yP(2:end);
        end
    end
    
    % PML边界条件应用（x方向）
    for j = 1:ny
        for k = 1:nz
            Pr(:,j,k) = Pr(:,j,k) .*weights_xP;
            tau_xx(:,j,k) = tau_xx(:,j,k) .*weights_xP;
            tau_yy(:,j,k) = tau_yy(:,j,k) .*weights_xP;
            tau_zz(:,j,k) = tau_zz(:,j,k) .*weights_xP;
            tau_xy(2:end-1,j,k) = tau_xy(2:end-1,j,k) .*weights_xP(2:end);
            tau_xz(2:end-1,j,k) = tau_xz(2:end-1,j,k) .*weights_xP(2:end);
            tau_yz(:,j,k) = tau_yz(:,j,k) .*weights_xP;
        end
    end

    % 速度场更新（FDTD算法）
    Vx(2:end-1,2:end-1,2:end-1) = Vx(2:end-1,2:end-1,2:end-1) + dt./rho_x(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xx(:,2:end-1,2:end-1) - Pr(:,2:end-1,2:end-1),1,1)/dx + diff(tau_xy(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_xz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    Vy(2:end-1,2:end-1,2:end-1) = Vy(2:end-1,2:end-1,2:end-1) + dt./rho_y(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xy(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yy(2:end-1,:,2:end-1) - Pr(2:end-1,:,2:end-1),1,2)/dy + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    Vz(2:end-1,2:end-1,2:end-1) = Vz(2:end-1,2:end-1,2:end-1) + dt./rho_z(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xz(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_zz(2:end-1,2:end-1,:) - Pr(2:end-1,2:end-1,:),1,3)/dz );
    
    % PML边界条件应用于速度场
    for j = 1:ny
        for k = 1:nz
            Vx(2:end-1,j,k) = Vx(2:end-1,j,k) .*weights_x(2:end-1);
        end
    end
    
    for i = 1:nx
        for k = 1:nz
            Vy(i,2:end-1,k) = Vy(i,2:end-1,k) .*weights_y(2:end-1);
        end
    end
    
    for i = 1:nx
        for j = 1:ny
            Vz(i,j,2:end-1) = Vz(i,j,2:end-1) .*weights_z(2:end-1);
        end
    end
    
end  % 主时间循环结束

%% ========================================================================
%  第八部分：结果处理
%% ========================================================================

% 模拟完成提示
fprintf('\n=== 终于他妈的跑完了 ===\n');

% 波形特征分析（用于数据保存）
max_amplitudes_geophone = max(abs(receiver_data), [], 1);
max_amplitudes_das = max(abs(das_strain_rate), [], 1);

% 数据保存（完整的声波测井数据 + DAS数据）
save_filename = sprintf('acoustic_logging_DAS_%dreceiver_results.mat', num_receivers);
save(save_filename, ...
     'receiver_data', 'das_strain_rate', 'src', 'time_axis', ...
     'receiver_x', 'receiver_y', 'receiver_z', ...
     'source_x', 'source_y', 'source_z', ...
     'source_distances', 'num_receivers', 'num_das_gauges', ...
     'first_receiver_offset', 'receiver_spacing', ...
     'gauge_length', 'gauge_overlap', ...
     'das_gauge_centers', 'das_gauge_start_z', 'das_gauge_end_z', ...
     'max_amplitudes_geophone', 'max_amplitudes_das', ...
     'dt', 'dx', 'dy', 'dz', 'nt', 'nx', 'ny', 'nz', ...
     'vp_fluid', 'vs_fluid', 'rho_fluid', ...
     'vp_rock', 'vs_rock', 'rho_rock', ...
     'well_radius', 'f0', 't0');

% 创建最终对比图（检波器vs DAS）
figure('Position', [200, 200, 1400, 800]);

% 绘制第2个检波器和第2个DAS标距的完整波形对比
if num_receivers >= 2 && num_das_gauges >= 2
    subplot(2,1,1);
    plot(time_axis*1e6, receiver_data(:,2), 'k-', 'LineWidth', 1.5);
    xlabel('时间 (μs)', 'FontSize', 12);
    ylabel('压力幅度', 'FontSize', 12);
    title('传统检波器波形 (第2个检波器) - 黑色', 'FontSize', 14);
    grid on;
    
    subplot(2,1,2);
    plot(time_axis*1e6, das_strain_rate(:,2), 'r-', 'LineWidth', 1.5);
    xlabel('时间 (μs)', 'FontSize', 12);
    ylabel('应变率', 'FontSize', 12);
    title('DAS应变率波形 (第2个标距) - 红色', 'FontSize', 14);
    grid on;
    
    % 保存对比图
    saveas(gcf, 'geophone_vs_das_comparison.png');
end

fprintf('\n=== 数据保存完成 ===\n');
fprintf('数据已保存到：%s\n', save_filename);
fprintf('包含%d个检波器和%d个DAS标距的完整波形数据\n', num_receivers, num_das_gauges);
fprintf('检波器配置：第一个检波器源距%.2fm，间距%.2fm\n', first_receiver_offset, receiver_spacing);
fprintf('DAS配置：标距长度%.2fm，覆盖范围%.2fm-%.2fm\n', gauge_length, das_gauge_centers(1), das_gauge_centers(end));
fprintf('对比图已保存为：geophone_vs_das_comparison.png\n');

%% ========================================================================
%  程序结束
%% ========================================================================